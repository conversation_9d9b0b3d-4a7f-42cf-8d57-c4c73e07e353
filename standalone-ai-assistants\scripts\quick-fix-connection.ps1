# Quick Fix for Connection Issues
# ==============================

Write-Host "🚀 Quick Connection Fix" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

# 1. Kill large VS Code process
Write-Host "`n1. Fixing VS Code memory issue..." -ForegroundColor Yellow
$largeVSCode = Get-Process -Name "Code" | Where-Object { $_.WorkingSet -gt 500MB }
if ($largeVSCode) {
    foreach ($proc in $largeVSCode) {
        $memoryMB = [math]::Round($proc.WorkingSet / 1MB, 1)
        Write-Host "   Killing large VS Code process: PID $($proc.Id) ($memoryMB MB)" -ForegroundColor Red
        Stop-Process -Id $proc.Id -Force
    }
    Write-Host "   ✅ Large VS Code processes killed" -ForegroundColor Green
} else {
    Write-Host "   ✅ No large VS Code processes found" -ForegroundColor Green
}

# 2. Clear DNS and reset connections
Write-Host "`n2. Clearing DNS cache..." -ForegroundColor Yellow
try {
    ipconfig /flushdns | Out-Null
    Write-Host "   ✅ DNS cache cleared" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Failed to clear DNS" -ForegroundColor Red
}

# 3. Kill hanging Node.js processes
Write-Host "`n3. Cleaning Node.js processes..." -ForegroundColor Yellow
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
$killed = 0
foreach ($proc in $nodeProcesses) {
    try {
        $wmi = Get-WmiObject Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue
        $cmdLine = if ($wmi) { $wmi.CommandLine } else { "" }
        
        # Kill npm-cache and unknown processes
        if ($cmdLine -match "npm-cache|_npx" -or ($proc.WorkingSet -lt 5MB -and $cmdLine -notmatch "anythingllm|ollama|n8n|ai-coordinator")) {
            Stop-Process -Id $proc.Id -Force
            $killed++
        }
    } catch {
        # Process already ended
    }
}
Write-Host "   ✅ Killed $killed Node.js processes" -ForegroundColor Green

# 4. Kill extra PowerShell processes
Write-Host "`n4. Cleaning PowerShell processes..." -ForegroundColor Yellow
$psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue | Where-Object { $_.Id -ne $PID -and $_.WorkingSet -lt 20MB }
$psKilled = 0
foreach ($proc in $psProcesses) {
    try {
        Stop-Process -Id $proc.Id -Force
        $psKilled++
    } catch {
        # Process already ended
    }
}
Write-Host "   ✅ Killed $psKilled PowerShell processes" -ForegroundColor Green

# 5. Start Docker if needed
Write-Host "`n5. Checking Docker..." -ForegroundColor Yellow
$dockerRunning = Get-Process -Name "Docker Desktop" -ErrorAction SilentlyContinue
if (-not $dockerRunning) {
    Write-Host "   🔄 Starting Docker Desktop..." -ForegroundColor Yellow
    try {
        Start-Process -FilePath "C:\Program Files\Docker\Docker\Docker Desktop.exe" -WindowStyle Hidden
        Write-Host "   ✅ Docker Desktop started" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠️  Could not start Docker Desktop" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ✅ Docker Desktop is running" -ForegroundColor Green
}

Write-Host "`n✅ QUICK FIX COMPLETED!" -ForegroundColor Green
Write-Host "Wait 30 seconds before retrying your operation" -ForegroundColor Yellow
