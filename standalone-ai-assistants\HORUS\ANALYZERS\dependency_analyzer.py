#!/usr/bin/env python3
"""
📦 HORUS - محلل التبعيات
Dependency Analyzer for HORUS System
"""

import json
import re
from pathlib import Path
from collections import defaultdict

class DependencyAnalyzer:
    """محلل التبعيات"""
    
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        
    def analyze(self):
        """تحليل التبعيات"""
        results = {
            "python_dependencies": self.analyze_python_dependencies(),
            "nodejs_dependencies": self.analyze_nodejs_dependencies(),
            "java_dependencies": self.analyze_java_dependencies(),
            "docker_dependencies": self.analyze_docker_dependencies(),
            "other_dependencies": self.analyze_other_dependencies(),
            "dependency_summary": {}
        }
        
        # إنشاء ملخص التبعيات
        results["dependency_summary"] = self.create_dependency_summary(results)
        
        return results
    
    def analyze_python_dependencies(self):
        """تحليل تبعيات Python"""
        dependencies = {
            "requirements_txt": [],
            "setup_py": [],
            "pyproject_toml": [],
            "pipfile": [],
            "conda_env": [],
            "imports": []
        }
        
        # requirements.txt
        req_file = self.project_path / "requirements.txt"
        if req_file.exists():
            dependencies["requirements_txt"] = self.parse_requirements_txt(req_file)
        
        # setup.py
        setup_file = self.project_path / "setup.py"
        if setup_file.exists():
            dependencies["setup_py"] = self.parse_setup_py(setup_file)
        
        # pyproject.toml
        pyproject_file = self.project_path / "pyproject.toml"
        if pyproject_file.exists():
            dependencies["pyproject_toml"] = self.parse_pyproject_toml(pyproject_file)
        
        # Pipfile
        pipfile = self.project_path / "Pipfile"
        if pipfile.exists():
            dependencies["pipfile"] = self.parse_pipfile(pipfile)
        
        # environment.yml (Conda)
        conda_file = self.project_path / "environment.yml"
        if conda_file.exists():
            dependencies["conda_env"] = self.parse_conda_env(conda_file)
        
        # تحليل الاستيرادات في ملفات Python
        dependencies["imports"] = self.analyze_python_imports()
        
        return dependencies
    
    def analyze_nodejs_dependencies(self):
        """تحليل تبعيات Node.js"""
        dependencies = {
            "package_json": {},
            "yarn_lock": [],
            "package_lock": []
        }
        
        # package.json
        package_file = self.project_path / "package.json"
        if package_file.exists():
            dependencies["package_json"] = self.parse_package_json(package_file)
        
        # yarn.lock
        yarn_lock = self.project_path / "yarn.lock"
        if yarn_lock.exists():
            dependencies["yarn_lock"] = self.parse_yarn_lock(yarn_lock)
        
        # package-lock.json
        package_lock = self.project_path / "package-lock.json"
        if package_lock.exists():
            dependencies["package_lock"] = self.parse_package_lock(package_lock)
        
        return dependencies
    
    def analyze_java_dependencies(self):
        """تحليل تبعيات Java"""
        dependencies = {
            "maven_pom": [],
            "gradle_build": [],
            "ivy_xml": []
        }
        
        # pom.xml (Maven)
        pom_file = self.project_path / "pom.xml"
        if pom_file.exists():
            dependencies["maven_pom"] = self.parse_maven_pom(pom_file)
        
        # build.gradle (Gradle)
        gradle_files = list(self.project_path.rglob("build.gradle*"))
        for gradle_file in gradle_files:
            dependencies["gradle_build"].extend(self.parse_gradle_build(gradle_file))
        
        return dependencies
    
    def analyze_docker_dependencies(self):
        """تحليل تبعيات Docker"""
        dependencies = {
            "dockerfile": [],
            "docker_compose": [],
            "base_images": [],
            "exposed_ports": []
        }
        
        # Dockerfile
        dockerfile = self.project_path / "Dockerfile"
        if dockerfile.exists():
            dependencies["dockerfile"] = self.parse_dockerfile(dockerfile)
        
        # docker-compose.yml
        compose_files = ["docker-compose.yml", "docker-compose.yaml"]
        for compose_file in compose_files:
            compose_path = self.project_path / compose_file
            if compose_path.exists():
                dependencies["docker_compose"] = self.parse_docker_compose(compose_path)
                break
        
        return dependencies
    
    def analyze_other_dependencies(self):
        """تحليل تبعيات أخرى"""
        dependencies = {
            "composer_php": [],
            "gemfile_ruby": [],
            "cargo_rust": [],
            "go_mod": []
        }
        
        # composer.json (PHP)
        composer_file = self.project_path / "composer.json"
        if composer_file.exists():
            dependencies["composer_php"] = self.parse_composer_json(composer_file)
        
        # Gemfile (Ruby)
        gemfile = self.project_path / "Gemfile"
        if gemfile.exists():
            dependencies["gemfile_ruby"] = self.parse_gemfile(gemfile)
        
        # Cargo.toml (Rust)
        cargo_file = self.project_path / "Cargo.toml"
        if cargo_file.exists():
            dependencies["cargo_rust"] = self.parse_cargo_toml(cargo_file)
        
        # go.mod (Go)
        go_mod = self.project_path / "go.mod"
        if go_mod.exists():
            dependencies["go_mod"] = self.parse_go_mod(go_mod)
        
        return dependencies
    
    def parse_requirements_txt(self, file_path):
        """تحليل requirements.txt"""
        dependencies = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # تحليل اسم الحزمة والإصدار
                        match = re.match(r'^([a-zA-Z0-9_-]+)([>=<!=~]+.*)?', line)
                        if match:
                            package_name = match.group(1)
                            version_spec = match.group(2) or ""
                            dependencies.append({
                                "name": package_name,
                                "version": version_spec,
                                "raw": line
                            })
        except Exception as e:
            pass
        
        return dependencies
    
    def parse_package_json(self, file_path):
        """تحليل package.json"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                return {
                    "name": data.get("name", ""),
                    "version": data.get("version", ""),
                    "dependencies": data.get("dependencies", {}),
                    "devDependencies": data.get("devDependencies", {}),
                    "peerDependencies": data.get("peerDependencies", {}),
                    "scripts": data.get("scripts", {}),
                    "engines": data.get("engines", {})
                }
        except Exception as e:
            return {}
    
    def parse_dockerfile(self, file_path):
        """تحليل Dockerfile"""
        instructions = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        parts = line.split(None, 1)
                        if len(parts) >= 2:
                            instruction = parts[0].upper()
                            value = parts[1]
                            instructions.append({
                                "line": line_num,
                                "instruction": instruction,
                                "value": value
                            })
        except Exception as e:
            pass
        
        return instructions
    
    def analyze_python_imports(self):
        """تحليل الاستيرادات في ملفات Python"""
        imports = defaultdict(int)
        
        for py_file in self.project_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # البحث عن import statements
                    import_patterns = [
                        r'^import\s+([a-zA-Z_][a-zA-Z0-9_]*)',
                        r'^from\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+import'
                    ]
                    
                    for pattern in import_patterns:
                        matches = re.findall(pattern, content, re.MULTILINE)
                        for match in matches:
                            # تجاهل الاستيرادات المحلية
                            if not match.startswith('.'):
                                imports[match] += 1
            except Exception as e:
                pass
        
        # تحويل إلى قائمة مرتبة
        sorted_imports = sorted(imports.items(), key=lambda x: x[1], reverse=True)
        
        return [{"module": module, "count": count} for module, count in sorted_imports[:50]]
    
    def create_dependency_summary(self, results):
        """إنشاء ملخص التبعيات"""
        summary = {
            "total_dependencies": 0,
            "by_language": {},
            "critical_dependencies": [],
            "outdated_patterns": [],
            "security_concerns": []
        }
        
        # عد التبعيات حسب اللغة
        python_deps = results.get("python_dependencies", {})
        if python_deps.get("requirements_txt"):
            summary["by_language"]["Python"] = len(python_deps["requirements_txt"])
            summary["total_dependencies"] += len(python_deps["requirements_txt"])
        
        nodejs_deps = results.get("nodejs_dependencies", {})
        package_json = nodejs_deps.get("package_json", {})
        if package_json.get("dependencies"):
            node_count = len(package_json["dependencies"]) + len(package_json.get("devDependencies", {}))
            summary["by_language"]["Node.js"] = node_count
            summary["total_dependencies"] += node_count
        
        # تحديد التبعيات الحرجة
        critical_packages = [
            "django", "flask", "fastapi", "express", "react", "vue", "angular",
            "spring", "hibernate", "junit", "docker", "kubernetes"
        ]
        
        # البحث عن التبعيات الحرجة
        all_deps = []
        if python_deps.get("requirements_txt"):
            all_deps.extend([dep["name"].lower() for dep in python_deps["requirements_txt"]])
        
        if package_json.get("dependencies"):
            all_deps.extend([name.lower() for name in package_json["dependencies"].keys()])
        
        summary["critical_dependencies"] = [
            dep for dep in critical_packages if dep in all_deps
        ]
        
        return summary
    
    # طرق تحليل إضافية (مبسطة)
    def parse_setup_py(self, file_path):
        return []
    
    def parse_pyproject_toml(self, file_path):
        return []
    
    def parse_pipfile(self, file_path):
        return []
    
    def parse_conda_env(self, file_path):
        return []
    
    def parse_yarn_lock(self, file_path):
        return []
    
    def parse_package_lock(self, file_path):
        return []
    
    def parse_maven_pom(self, file_path):
        return []
    
    def parse_gradle_build(self, file_path):
        return []
    
    def parse_docker_compose(self, file_path):
        return []
    
    def parse_composer_json(self, file_path):
        return []
    
    def parse_gemfile(self, file_path):
        return []
    
    def parse_cargo_toml(self, file_path):
        return []
    
    def parse_go_mod(self, file_path):
        return []
