# 🤖 دليل استخدام النظام المعزول
## How to Use the Isolated System

---

## 🚀 **الطرق المختلفة للاستخدام**

### 1. **⚡ الطريقة الأسرع (مُوصى بها)**

```bash
# انقر مرتين على الملف
START-SIMPLE.bat
```

**أو في Terminal:**
```bash
cd standalone-ai-assistants
python simple-interface.py
```

---

### 2. **📊 تحليل المشروع الكبير مباشرة**

```bash
python analyze-big-project.py
```

**النتيجة:**
- تحليل شامل للمشروع الكبير
- تقرير مفصل في `shared-memory/big-project-analysis.json`
- خطة عمل واضحة

---

### 3. **🧠 استخدام الواجهة التفاعلية الأصلية**

```bash
python quick-start.py
```

**الخيارات:**
- `1` - استشارة Gemini CLI
- `2` - عرض حالة النظام
- `3` - عرض حالة الذاكرة
- `4` - اختبار المساعدين
- `5` - عرض تاريخ الاستشارات

---

### 4. **🌐 الواجهة الويب**

```bash
# افتح في المتصفح
quick-web-interface.html
```

---

## 🎯 **الاستخدامات العملية**

### 📊 **تحليل المشروع**
```bash
# تحليل شامل
python analyze-big-project.py

# عرض النتائج
cat shared-memory/big-project-analysis.json
```

### 🔍 **البحث في الملفات**
```bash
# استخدم الواجهة البسيطة
python simple-interface.py
# ثم اختر رقم 4
```

### 📝 **إنشاء التقارير**
```bash
# تقرير يدوي
python simple-interface.py
# ثم اختر رقم 3
```

### 🧠 **عرض الذاكرة**
```bash
# عرض الذاكرة المشتركة
python simple-interface.py
# ثم اختر رقم 2
```

---

## 🛠️ **الأوامر المفيدة**

### فحص حالة النظام
```bash
python simple-interface.py
# اختر رقم 5
```

### تنظيف النظام
```bash
# تشغيل سكريبت التنظيف
scripts/comprehensive-cleanup.ps1
```

### فحص المسارات
```bash
python scripts/check-paths.py
```

---

## 📁 **الملفات المهمة**

| الملف | الوصف |
|-------|--------|
| `simple-interface.py` | الواجهة البسيطة (مُوصى بها) |
| `analyze-big-project.py` | تحليل المشروع الكبير |
| `quick-start.py` | الواجهة التفاعلية الأصلية |
| `START-SIMPLE.bat` | تشغيل سريع للواجهة |
| `shared-memory/` | الذاكرة المشتركة |
| `scripts/` | سكريبتات مساعدة |

---

## 🎯 **أمثلة عملية**

### مثال 1: تحليل سريع
```bash
# 1. تشغيل التحليل
python analyze-big-project.py

# 2. عرض النتائج
python simple-interface.py
# اختر رقم 2 (عرض الذاكرة)
```

### مثال 2: البحث عن ملفات Python
```bash
# 1. تشغيل الواجهة
python simple-interface.py

# 2. اختر رقم 4 (البحث)
# 3. أدخل: python
```

### مثال 3: إنشاء تقرير مخصص
```bash
# 1. تشغيل الواجهة
python simple-interface.py

# 2. اختر رقم 3 (إنشاء تقرير)
# 3. سيتم حفظ التقرير في 08-reports/
```

---

## ⚠️ **نصائح مهمة**

### ✅ **افعل**
- استخدم `simple-interface.py` للبداية
- راجع `shared-memory/` للبيانات المحفوظة
- استخدم `analyze-big-project.py` للتحليل الشامل

### ❌ **لا تفعل**
- لا تحذف ملفات `shared-memory/`
- لا تعدل الملفات الأساسية بدون نسخ احتياطي
- لا تشغل عدة واجهات في نفس الوقت

---

## 🆘 **حل المشاكل**

### مشكلة: "No module named..."
```bash
# تأكد من وجود Python
python --version

# تأكد من المسار الصحيح
cd standalone-ai-assistants
```

### مشكلة: "File not found"
```bash
# فحص الملفات
python simple-interface.py
# اختر رقم 5 (حالة النظام)
```

### مشكلة: واجهة لا تستجيب
```bash
# إيقاف العملية
Ctrl + C

# إعادة التشغيل
python simple-interface.py
```

---

## 📞 **الدعم السريع**

### أوامر التشخيص
```bash
# فحص شامل
python simple-interface.py

# فحص المسارات
python scripts/check-paths.py

# تنظيف شامل
scripts/comprehensive-cleanup.ps1
```

### ملفات السجلات
- `logs/` - سجلات النظام
- `shared-memory/` - البيانات المحفوظة
- `08-reports/` - التقارير المُنشأة

---

## 🎯 **الخلاصة**

**للاستخدام السريع:**
1. انقر على `START-SIMPLE.bat`
2. أو شغل `python simple-interface.py`
3. اختر الخيار المناسب

**للتحليل الشامل:**
1. شغل `python analyze-big-project.py`
2. راجع النتائج في `shared-memory/`

**للبحث والاستكشاف:**
1. استخدم الواجهة البسيطة
2. اختر البحث في الملفات
3. راجع حالة النظام

---

**📅 تم الإنشاء:** 2025-07-07  
**🔄 آخر تحديث:** 2025-07-07  
**👤 المطور:** Augment Agent  
**🛡️ الحالة:** جاهز للاستخدام
