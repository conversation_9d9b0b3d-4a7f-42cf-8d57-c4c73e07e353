{"horus_memory": {"version": "1.0.0", "created": "2025-07-07T10:00:00Z", "last_updated": "2025-07-07T11:09:41.343538", "total_projects_analyzed": 1, "memory_size_mb": 0.1}, "projects": {"_2377": {"name": "", "path": ".", "first_analyzed": "2025-07-07T11:09:41.343515", "analysis_count": 1, "analyses": [{"analysis_id": "ab6ab30a-8ee6-4c04-9bcd-cf63b79d0a86", "timestamp": "2025-07-07T11:09:40.995786", "summary": {"project_types": [{"type": "Python", "confidence": 30, "indicators_found": 3}], "total_files": 18, "quality_score": 56.5, "quality_grade": "يحتاج تحسين", "recommendations_count": 4}}], "last_analyzed": "2025-07-07T11:09:41.343526"}}, "analysis_history": [], "learning_patterns": {"common_project_types": {}, "frequent_issues": {}, "best_practices": {}, "optimization_suggestions": {}}, "user_preferences": {"default_analysis_depth": "deep", "preferred_report_format": "json", "auto_save_reports": true, "enable_learning": true}, "statistics": {"most_analyzed_language": null, "average_project_size": 0, "common_frameworks": {}, "analysis_success_rate": 100.0}}