#!/usr/bin/env python3
"""
🦅 HORUS - محلل المشاريع الرئيسي
Main Project Analyzer for HORUS System
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime
import uuid

# إضافة مسار HORUS للاستيراد
sys.path.append(str(Path(__file__).parent.parent))

try:
    from ANALYZERS.structure_analyzer import StructureAnalyzer
    from ANALYZERS.dependency_analyzer import DependencyAnalyzer
    from ANALYZERS.quality_analyzer import QualityAnalyzer
    from MEMORY.memory_manager import MemoryManager
except ImportError:
    # في حالة عدم وجود الملفات، استخدم نسخ مبسطة
    from pathlib import Path
    import json

    class StructureAnalyzer:
        def __init__(self, project_path):
            self.project_path = Path(project_path)

        def analyze(self):
            return {"project_types": ["Unknown"], "file_statistics": {"total_files": 0}}

    class DependencyAnalyzer:
        def __init__(self, project_path):
            self.project_path = Path(project_path)

        def analyze(self):
            return {"dependency_summary": {"total_dependencies": 0}}

    class QualityAnalyzer:
        def __init__(self, project_path):
            self.project_path = Path(project_path)

        def analyze(self):
            return {"quality_metrics": {"overall_quality_score": 0, "quality_grade": "غير محدد"}}

    class MemoryManager:
        def save_analysis(self, results):
            pass

        def get_smart_recommendations(self, *args):
            return []

class HorusProjectAnalyzer:
    """محلل المشاريع الرئيسي لنظام HORUS"""
    
    def __init__(self, project_path=None):
        self.project_path = Path(project_path) if project_path else Path(".").resolve()
        self.project_name = self.project_path.name
        self.analysis_id = str(uuid.uuid4())
        self.timestamp = datetime.now()
        
        # تهيئة المحللات الفرعية
        self.structure_analyzer = StructureAnalyzer(self.project_path)
        self.dependency_analyzer = DependencyAnalyzer(self.project_path)
        self.quality_analyzer = QualityAnalyzer(self.project_path)
        
        # تهيئة مدير الذاكرة
        self.memory_manager = MemoryManager()
        
        # نتائج التحليل
        self.results = {
            "metadata": {
                "analysis_id": self.analysis_id,
                "timestamp": self.timestamp.isoformat(),
                "analyzer_version": "HORUS 1.0.0",
                "project_name": self.project_name,
                "project_path": str(self.project_path)
            }
        }
    
    def run_full_analysis(self):
        """تشغيل التحليل الكامل"""
        print(f"🦅 HORUS - بدء التحليل الشامل")
        print(f"📁 المشروع: {self.project_name}")
        print(f"📍 المسار: {self.project_path}")
        print(f"🆔 معرف التحليل: {self.analysis_id}")
        print("=" * 60)
        
        try:
            # 1. تحليل الهيكل
            print("🏗️ تحليل هيكل المشروع...")
            structure_results = self.structure_analyzer.analyze()
            self.results["structure"] = structure_results
            print("✅ تحليل الهيكل مكتمل")
            
            # 2. تحليل التبعيات
            print("📦 تحليل التبعيات...")
            dependency_results = self.dependency_analyzer.analyze()
            self.results["dependencies"] = dependency_results
            print("✅ تحليل التبعيات مكتمل")
            
            # 3. تحليل الجودة
            print("📊 تحليل جودة الكود...")
            quality_results = self.quality_analyzer.analyze()
            self.results["quality"] = quality_results
            print("✅ تحليل الجودة مكتمل")
            
            # 4. إنشاء التوصيات
            print("💡 إنشاء التوصيات...")
            recommendations = self.generate_recommendations()
            self.results["recommendations"] = recommendations
            print("✅ التوصيات مكتملة")
            
            # 5. حفظ في الذاكرة
            print("🧠 حفظ في الذاكرة...")
            self.memory_manager.save_analysis(self.results)
            print("✅ تم الحفظ في الذاكرة")
            
            # 6. إنشاء التقرير
            print("📄 إنشاء التقرير...")
            report_path = self.save_report()
            print(f"✅ التقرير محفوظ: {report_path}")
            
            # 7. عرض الملخص
            self.display_summary()
            
            return self.results
            
        except Exception as e:
            print(f"❌ خطأ في التحليل: {e}")
            return None
    
    def generate_recommendations(self):
        """إنشاء التوصيات الذكية"""
        recommendations = []
        
        # استخراج البيانات
        structure = self.results.get("structure", {})
        dependencies = self.results.get("dependencies", {})
        quality = self.results.get("quality", {})
        
        project_types = structure.get("project_types", [])
        total_files = structure.get("total_files", 0)
        test_ratio = quality.get("test_ratio_numeric", 0)
        doc_ratio = quality.get("doc_ratio_numeric", 0)
        
        # توصيات عامة
        if total_files > 1000:
            recommendations.append({
                "type": "structure",
                "priority": "high",
                "title": "تنظيم الملفات",
                "description": "المشروع كبير، يُنصح بتنظيم الملفات في مجلدات فرعية"
            })
        
        if test_ratio < 20:
            recommendations.append({
                "type": "testing",
                "priority": "high",
                "title": "زيادة الاختبارات",
                "description": f"نسبة الاختبارات منخفضة ({test_ratio:.1f}%)، يُنصح بإضافة المزيد"
            })
        
        if doc_ratio < 10:
            recommendations.append({
                "type": "documentation",
                "priority": "medium",
                "title": "تحسين التوثيق",
                "description": f"نسبة التوثيق منخفضة ({doc_ratio:.1f}%)، يُنصح بإضافة README وتوثيق"
            })
        
        # توصيات حسب نوع المشروع
        if "Python" in project_types:
            recommendations.extend([
                {
                    "type": "python",
                    "priority": "medium",
                    "title": "استخدام Virtual Environment",
                    "description": "إنشاء بيئة افتراضية للمشروع"
                },
                {
                    "type": "python",
                    "priority": "low",
                    "title": "تنسيق الكود",
                    "description": "استخدام Black أو autopep8 لتنسيق الكود"
                }
            ])
        
        if "Node.js" in project_types:
            recommendations.extend([
                {
                    "type": "nodejs",
                    "priority": "medium",
                    "title": "فحص الكود",
                    "description": "استخدام ESLint لفحص جودة الكود"
                },
                {
                    "type": "nodejs",
                    "priority": "low",
                    "title": "تنسيق الكود",
                    "description": "استخدام Prettier لتنسيق الكود"
                }
            ])
        
        if "Docker" in project_types:
            recommendations.extend([
                {
                    "type": "docker",
                    "priority": "medium",
                    "title": "تحسين Dockerfile",
                    "description": "استخدام multi-stage builds لتقليل حجم الصورة"
                },
                {
                    "type": "docker",
                    "priority": "high",
                    "title": "فحص الأمان",
                    "description": "فحص الحاويات للثغرات الأمنية"
                }
            ])
        
        # استخدام الذاكرة للتوصيات الذكية
        memory_recommendations = self.memory_manager.get_smart_recommendations(
            project_types, total_files, test_ratio, doc_ratio
        )
        recommendations.extend(memory_recommendations)
        
        return recommendations
    
    def save_report(self):
        """حفظ التقرير"""
        # إنشاء مجلد التقارير
        reports_dir = Path(__file__).parent.parent / "REPORTS" / "json"
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        # اسم الملف
        timestamp_str = self.timestamp.strftime("%Y%m%d_%H%M%S")
        filename = f"horus_analysis_{self.project_name}_{timestamp_str}.json"
        report_path = reports_dir / filename
        
        # حفظ التقرير
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        return report_path
    
    def display_summary(self):
        """عرض ملخص النتائج"""
        print("\n🦅 HORUS - ملخص التحليل")
        print("=" * 40)
        
        structure = self.results.get("structure", {})
        quality = self.results.get("quality", {})
        recommendations = self.results.get("recommendations", [])
        
        print(f"📁 اسم المشروع: {structure.get('project_name', 'غير محدد')}")
        print(f"🏷️ نوع المشروع: {', '.join(structure.get('project_types', ['غير محدد']))}")
        print(f"📄 إجمالي الملفات: {structure.get('total_files', 0):,}")
        print(f"📂 إجمالي المجلدات: {structure.get('total_directories', 0):,}")
        print(f"📝 ملفات التوثيق: {quality.get('documentation_files', 0)}")
        print(f"🧪 ملفات الاختبار: {quality.get('test_files', 0)}")
        print(f"📊 نسبة التوثيق: {quality.get('doc_ratio', '0%')}")
        print(f"🔬 نسبة الاختبارات: {quality.get('test_ratio', '0%')}")
        
        # عرض التوصيات
        high_priority = [r for r in recommendations if r.get('priority') == 'high']
        medium_priority = [r for r in recommendations if r.get('priority') == 'medium']
        
        print(f"\n💡 التوصيات:")
        print(f"  🔴 عالية الأولوية: {len(high_priority)}")
        print(f"  🟡 متوسطة الأولوية: {len(medium_priority)}")
        print(f"  📊 إجمالي التوصيات: {len(recommendations)}")
        
        if high_priority:
            print(f"\n🔴 أهم التوصيات:")
            for i, rec in enumerate(high_priority[:3], 1):
                print(f"  {i}. {rec.get('title', 'غير محدد')}")

def main():
    """الدالة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(description="🦅 HORUS - محلل المشاريع المتقدم")
    parser.add_argument("--project", "-p", help="مسار المشروع")
    parser.add_argument("--output", "-o", help="مجلد الإخراج")
    
    args = parser.parse_args()
    
    # تحديد مسار المشروع
    project_path = args.project if args.project else "."
    
    # إنشاء المحلل
    analyzer = HorusProjectAnalyzer(project_path)
    
    # تشغيل التحليل
    results = analyzer.run_full_analysis()
    
    if results:
        print(f"\n🎉 تم الانتهاء من التحليل بنجاح!")
        print(f"🆔 معرف التحليل: {results['metadata']['analysis_id']}")
    else:
        print(f"\n❌ فشل في التحليل")

if __name__ == "__main__":
    main()
