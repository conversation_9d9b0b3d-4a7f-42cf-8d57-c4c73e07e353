# 🦅 دليل استخدام نظام HORUS
## HORUS Advanced Project Analyzer - Complete Usage Guide

---

## 🎯 **مرحباً بك في نظام HORUS!**

**HORUS** هو نظام متقدم ومنفصل لتحليل المشاريع البرمجية. تم تصميمه ليكون محلل مستقل وشامل لأي نوع من المشاريع.

---

## 🚀 **البدء السريع**

### **الطريقة الأسرع:**
```bash
# انقر مرتين على الملف
START-HORUS.bat
```

### **من Terminal:**
```bash
cd HORUS
python INTERFACES/main_interface.py
```

### **تحليل مباشر:**
```bash
# تحليل المشروع الحالي
python ANALYZERS/project_analyzer.py

# تحليل مشروع آخر
python ANALYZERS/project_analyzer.py --project "path/to/project"
```

---

## 🏗️ **هيكل نظام HORUS**

```
🦅 HORUS/
├── 🧠 MEMORY/              # ذاكرة النظام المتقدمة
│   ├── projects_database.json
│   ├── analysis_history.json
│   └── memory_manager.py
│
├── 🔍 ANALYZERS/           # محركات التحليل
│   ├── project_analyzer.py      # المحلل الرئيسي
│   ├── structure_analyzer.py    # محلل الهيكل
│   ├── dependency_analyzer.py   # محلل التبعيات
│   └── quality_analyzer.py      # محلل الجودة
│
├── 📊 REPORTS/             # التقارير والنتائج
│   ├── json/              # تقارير JSON
│   └── html/              # تقارير HTML (قريباً)
│
├── 🎯 INTERFACES/          # واجهات المستخدم
│   └── main_interface.py   # الواجهة الرئيسية
│
├── 🛠️ TOOLS/              # أدوات مساعدة
│   └── report_viewer.py    # عارض التقارير
│
├── 📚 DOCS/               # التوثيق
│   └── HORUS-USAGE-GUIDE.md
│
├── ⚙️ CONFIG/             # ملفات التكوين
│   └── horus_config.json
│
├── 🦅 README.md           # الدليل الرئيسي
└── 🚀 START-HORUS.bat     # تشغيل سريع
```

---

## 🎯 **الواجهة الرئيسية**

عند تشغيل HORUS، ستظهر لك الخيارات التالية:

### **1. 🔍 تحليل مشروع جديد**
- تحليل المشروع الحالي
- تحليل مشروع آخر

### **2. 📊 عرض آخر تحليل**
- مراجعة نتائج التحليل السابق

### **3. 📋 عرض تاريخ التحليلات**
- تاريخ جميع التحليلات
- إحصائيات المشاريع المحللة

### **4. 🧠 إحصائيات الذاكرة**
- حالة ذاكرة النظام
- بيانات التعلم

### **5. 📄 عرض التقارير المحفوظة**
- قائمة جميع التقارير
- إمكانية مراجعة التقارير السابقة

### **6. 🔧 إعدادات النظام**
- تكوين النظام
- أدوات الصيانة

### **7. ℹ️ معلومات حول HORUS**
- تفاصيل النظام والمطور

---

## 🔍 **محركات التحليل**

### **1. 🏗️ محلل الهيكل (Structure Analyzer)**
- اكتشاف نوع المشروع تلقائياً
- إحصائيات الملفات والمجلدات
- تحليل التعقيد
- الملفات الكبيرة

### **2. 📦 محلل التبعيات (Dependency Analyzer)**
- Python: requirements.txt, setup.py, pyproject.toml
- Node.js: package.json, yarn.lock
- Java: pom.xml, build.gradle
- Docker: Dockerfile, docker-compose.yml
- وأكثر...

### **3. 📊 محلل الجودة (Quality Analyzer)**
- تصنيف الملفات (كود، اختبارات، توثيق)
- تقييم جودة التوثيق
- تقدير تغطية الاختبارات
- تحليل تنظيم الكود
- اصطلاحات التسمية

---

## 🧠 **نظام الذاكرة المتقدم**

### **المميزات:**
- حفظ تاريخ جميع التحليلات
- تعلم من المشاريع السابقة
- توصيات ذكية بناءً على التجربة
- مقارنة التطور عبر الزمن

### **الملفات:**
- `projects_database.json` - قاعدة بيانات المشاريع
- `analysis_history.json` - تاريخ التحليلات
- `learning_data.json` - بيانات التعلم

---

## 📊 **التقارير**

### **تنسيق JSON:**
```json
{
  "metadata": {
    "analysis_id": "uuid",
    "timestamp": "2025-07-07T10:00:00",
    "project_name": "MyProject",
    "analyzer_version": "HORUS 1.0.0"
  },
  "structure": {
    "project_types": [...],
    "file_statistics": {...},
    "complexity_metrics": {...}
  },
  "dependencies": {
    "python_dependencies": {...},
    "nodejs_dependencies": {...}
  },
  "quality": {
    "quality_metrics": {
      "overall_quality_score": 85.5,
      "quality_grade": "جيد جداً"
    }
  },
  "recommendations": [...]
}
```

### **مكان الحفظ:**
- `REPORTS/json/horus_analysis_ProjectName_YYYYMMDD_HHMMSS.json`

---

## 💡 **نظام التوصيات الذكي**

### **أنواع التوصيات:**
- **🔴 عالية الأولوية**: مشاكل حرجة تحتاج إصلاح فوري
- **🟡 متوسطة الأولوية**: تحسينات مهمة
- **🟢 منخفضة الأولوية**: تحسينات اختيارية

### **التوصيات حسب نوع المشروع:**
- **Python**: Virtual Environment, Black, pytest
- **Node.js**: ESLint, Prettier, Jest, TypeScript
- **Docker**: Multi-stage builds, Health checks
- **عامة**: تحسين التوثيق، زيادة الاختبارات

---

## 🎯 **المشاريع المدعومة**

✅ **Python** (Django, Flask, FastAPI, etc.)  
✅ **Node.js** (React, Vue, Angular, Express, etc.)  
✅ **Java** (Spring, Maven, Gradle)  
✅ **C#** (.NET, ASP.NET, Blazor)  
✅ **PHP** (Laravel, Symfony, WordPress)  
✅ **Ruby** (Rails, Sinatra)  
✅ **Go** (Gin, Echo, Fiber)  
✅ **Rust** (Actix, Rocket, Axum)  
✅ **Docker** (أي مشروع بحاويات)  
✅ **Web** (HTML, CSS, JavaScript)  
✅ **Mobile** (React Native, Flutter)  
✅ **أي مشروع برمجي آخر**  

---

## 🛠️ **أدوات الصيانة**

### **تنظيف الذاكرة:**
- حذف التحليلات القديمة (أكثر من 365 يوم)
- تحسين حجم قاعدة البيانات

### **النسخ الاحتياطي:**
- نسخ احتياطي للذاكرة والتقارير
- استرداد البيانات

### **تصدير البيانات:**
- تصدير التقارير بتنسيقات مختلفة
- تصدير إحصائيات الذاكرة

---

## ⚙️ **التكوين المتقدم**

### **ملف التكوين:** `CONFIG/horus_config.json`

```json
{
  "analysis": {
    "default_depth": "deep",
    "enable_learning": true,
    "auto_save_reports": true
  },
  "memory": {
    "retention_days": 365,
    "auto_cleanup": true
  },
  "reports": {
    "default_format": "json",
    "include_recommendations": true
  }
}
```

---

## 🆘 **حل المشاكل**

### **مشكلة: "لا يمكن اكتشاف نوع المشروع"**
- تأكد من وجود ملفات مميزة (package.json, requirements.txt, etc.)
- تأكد من وجود ملفات كود بالامتدادات المناسبة

### **مشكلة: "خطأ في قراءة الملفات"**
- تأكد من صلاحيات القراءة
- تأكد من صحة مسار المشروع

### **مشكلة: "الذاكرة لا تعمل"**
- تأكد من وجود مجلد MEMORY
- تأكد من صلاحيات الكتابة

---

## 📈 **الإحصائيات والمقاييس**

### **مقاييس الجودة:**
- **90-100**: ممتاز
- **80-89**: جيد جداً  
- **70-79**: جيد
- **60-69**: مقبول
- **أقل من 60**: يحتاج تحسين

### **العوامل المؤثرة:**
- نسبة التوثيق (25%)
- تغطية الاختبارات (30%)
- تنظيم الكود (20%)
- اصطلاحات التسمية (15%)
- التعقيد (10%)

---

## 🚀 **الخطط المستقبلية**

### **الإصدار 1.1:**
- واجهة ويب تفاعلية
- تقارير HTML/PDF
- تحليل الأمان المتقدم

### **الإصدار 1.2:**
- تكامل مع Git
- تحليل الأداء
- ذكاء اصطناعي للتوصيات

### **الإصدار 2.0:**
- نظام إضافات (Plugins)
- تحليل متعدد المشاريع
- لوحة تحكم شاملة

---

## 📞 **الدعم والمساعدة**

### **الأوامر المساعدة:**
```bash
# مساعدة عامة
python INTERFACES/main_interface.py --help

# تشخيص النظام
python TOOLS/diagnostic_tools.py

# حالة الذاكرة
python MEMORY/memory_manager.py --status
```

### **الملفات المهمة:**
- `MEMORY/projects_database.json` - قاعدة البيانات
- `CONFIG/horus_config.json` - التكوين
- `REPORTS/json/` - التقارير المحفوظة

---

## 🏆 **مميزات HORUS الفريدة**

### **🧠 ذكاء متقدم:**
- تعلم من التحليلات السابقة
- توصيات ذكية مخصصة
- مقارنات زمنية

### **🔍 تحليل شامل:**
- 6 محركات تحليل متخصصة
- دعم 15+ لغة برمجة
- اكتشاف تلقائي لنوع المشروع

### **📊 تقارير احترافية:**
- تقارير JSON مفصلة
- إحصائيات مرئية
- ملخصات تنفيذية

### **🛡️ أمان وموثوقية:**
- نظام منفصل ومعزول
- نسخ احتياطي تلقائي
- حماية البيانات

---

**🦅 HORUS - حيث يلتقي التحليل المتقدم بالذكاء الاصطناعي!**

---

**📅 تم الإنشاء:** 2025-07-07  
**👤 المطور:** Augment Agent  
**📝 الإصدار:** 1.0.0  
**🦅 النظام:** HORUS Advanced Project Analyzer
