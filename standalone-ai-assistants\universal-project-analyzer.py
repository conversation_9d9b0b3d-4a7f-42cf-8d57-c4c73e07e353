#!/usr/bin/env python3
"""
محلل المشاريع العام - Universal Project Analyzer
يمكن استخدامه مع أي مشروع برمجي
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

class UniversalProjectAnalyzer:
    """محلل عام لأي مشروع"""
    
    def __init__(self, project_path=None):
        self.project_path = Path(project_path) if project_path else Path("..").resolve()
        self.project_name = self.project_path.name
        self.results = {}
        
    def detect_project_type(self):
        """اكتشاف نوع المشروع"""
        project_indicators = {
            "Python": ["requirements.txt", "setup.py", "pyproject.toml", "*.py"],
            "Node.js": ["package.json", "node_modules", "*.js", "*.ts"],
            "Java": ["pom.xml", "build.gradle", "*.java"],
            "C#": ["*.csproj", "*.sln", "*.cs"],
            "PHP": ["composer.json", "*.php"],
            "<PERSON>": ["Gemfile", "*.rb"],
            "Go": ["go.mod", "*.go"],
            "Rust": ["Cargo.toml", "*.rs"],
            "Docker": ["Dockerfile", "docker-compose.yml"],
            "Web": ["index.html", "*.css", "*.html"],
            "React": ["package.json", "src/", "public/"],
            "Vue": ["vue.config.js", "*.vue"],
            "Angular": ["angular.json", "*.component.ts"]
        }
        
        detected_types = []
        
        for project_type, indicators in project_indicators.items():
            for indicator in indicators:
                if "*" in indicator:
                    # البحث عن ملفات بامتداد معين
                    extension = indicator.replace("*", "")
                    if list(self.project_path.rglob(f"*{extension}")):
                        detected_types.append(project_type)
                        break
                else:
                    # البحث عن ملفات أو مجلدات محددة
                    if (self.project_path / indicator).exists():
                        detected_types.append(project_type)
                        break
        
        return detected_types
    
    def analyze_structure(self):
        """تحليل هيكل المشروع"""
        print(f"🔍 تحليل هيكل المشروع: {self.project_name}")
        
        # إحصائيات أساسية
        total_files = 0
        total_dirs = 0
        file_types = {}
        large_files = []
        
        for item in self.project_path.rglob("*"):
            if item.is_file():
                total_files += 1
                extension = item.suffix.lower()
                file_types[extension] = file_types.get(extension, 0) + 1
                
                # فحص الملفات الكبيرة (أكبر من 1MB)
                try:
                    size_mb = item.stat().st_size / (1024 * 1024)
                    if size_mb > 1:
                        large_files.append((str(item.relative_to(self.project_path)), f"{size_mb:.1f}MB"))
                except:
                    pass
            elif item.is_dir():
                total_dirs += 1
        
        # اكتشاف نوع المشروع
        project_types = self.detect_project_type()
        
        structure_analysis = {
            "project_name": self.project_name,
            "project_path": str(self.project_path),
            "project_types": project_types,
            "total_files": total_files,
            "total_directories": total_dirs,
            "file_types": dict(sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]),
            "large_files": large_files[:10],
            "main_directories": [d.name for d in self.project_path.iterdir() if d.is_dir()][:10]
        }
        
        self.results["structure"] = structure_analysis
        return structure_analysis
    
    def analyze_dependencies(self):
        """تحليل التبعيات"""
        print("📦 تحليل التبعيات...")
        
        dependencies = {}
        
        # Python
        req_file = self.project_path / "requirements.txt"
        if req_file.exists():
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    python_deps = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                dependencies["Python"] = python_deps[:20]  # أول 20 تبعية
            except:
                pass
        
        # Node.js
        package_file = self.project_path / "package.json"
        if package_file.exists():
            try:
                with open(package_file, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                    node_deps = list(package_data.get('dependencies', {}).keys())
                    dev_deps = list(package_data.get('devDependencies', {}).keys())
                dependencies["Node.js"] = {
                    "dependencies": node_deps[:15],
                    "devDependencies": dev_deps[:15]
                }
            except:
                pass
        
        # Docker
        docker_file = self.project_path / "docker-compose.yml"
        if docker_file.exists():
            dependencies["Docker"] = "docker-compose.yml موجود"
        
        self.results["dependencies"] = dependencies
        return dependencies
    
    def analyze_code_quality(self):
        """تحليل جودة الكود"""
        print("📊 تحليل جودة الكود...")
        
        quality_metrics = {
            "documentation": 0,
            "tests": 0,
            "config_files": 0,
            "code_files": 0
        }
        
        doc_patterns = ["README", "CHANGELOG", "LICENSE", "CONTRIBUTING", ".md", ".rst", ".txt"]
        test_patterns = ["test", "spec", "__test__", "tests"]
        config_patterns = [".json", ".yml", ".yaml", ".toml", ".ini", ".cfg", ".env"]
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                file_name = file_path.name.lower()
                
                # ملفات التوثيق
                if any(pattern.lower() in file_name for pattern in doc_patterns):
                    quality_metrics["documentation"] += 1
                
                # ملفات الاختبار
                if any(pattern in file_name for pattern in test_patterns):
                    quality_metrics["tests"] += 1
                
                # ملفات التكوين
                if any(file_name.endswith(pattern) for pattern in config_patterns):
                    quality_metrics["config_files"] += 1
                
                # ملفات الكود
                code_extensions = ['.py', '.js', '.ts', '.java', '.cs', '.php', '.rb', '.go', '.rs']
                if any(file_name.endswith(ext) for ext in code_extensions):
                    quality_metrics["code_files"] += 1
        
        # حساب النسب
        total_files = sum(quality_metrics.values())
        if total_files > 0:
            quality_metrics["doc_ratio"] = f"{(quality_metrics['documentation'] / total_files * 100):.1f}%"
            quality_metrics["test_ratio"] = f"{(quality_metrics['tests'] / total_files * 100):.1f}%"
        
        self.results["code_quality"] = quality_metrics
        return quality_metrics
    
    def generate_recommendations(self):
        """إنشاء التوصيات"""
        print("💡 إنشاء التوصيات...")
        
        recommendations = []
        structure = self.results.get("structure", {})
        dependencies = self.results.get("dependencies", {})
        quality = self.results.get("code_quality", {})
        
        # توصيات بناءً على نوع المشروع
        project_types = structure.get("project_types", [])
        
        if "Python" in project_types:
            recommendations.extend([
                "إضافة virtual environment للمشروع",
                "استخدام black لتنسيق الكود",
                "إضافة pytest للاختبارات",
                "استخدام pre-commit hooks"
            ])
        
        if "Node.js" in project_types:
            recommendations.extend([
                "استخدام ESLint لفحص الكود",
                "إضافة Prettier لتنسيق الكود",
                "استخدام Jest للاختبارات",
                "إضافة TypeScript للأمان"
            ])
        
        if "Docker" in project_types:
            recommendations.extend([
                "تحسين Dockerfile للأداء",
                "استخدام multi-stage builds",
                "إضافة health checks",
                "تحسين أمان الحاويات"
            ])
        
        # توصيات عامة
        if quality.get("documentation", 0) < 3:
            recommendations.append("إضافة المزيد من التوثيق (README, CHANGELOG)")
        
        if quality.get("tests", 0) < 5:
            recommendations.append("إضافة المزيد من الاختبارات")
        
        if structure.get("total_files", 0) > 1000:
            recommendations.append("تنظيم الملفات في مجلدات فرعية")
        
        self.results["recommendations"] = recommendations
        return recommendations
    
    def save_analysis_report(self, output_file=None):
        """حفظ تقرير التحليل"""
        if not output_file:
            output_file = f"analysis-{self.project_name}-{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "analyzer": "Universal Project Analyzer",
            "project": self.project_name,
            "project_path": str(self.project_path),
            "analysis_results": self.results,
            "summary": {
                "project_types": self.results.get("structure", {}).get("project_types", []),
                "total_files": self.results.get("structure", {}).get("total_files", 0),
                "main_languages": list(self.results.get("structure", {}).get("file_types", {}).keys())[:5],
                "has_tests": self.results.get("code_quality", {}).get("tests", 0) > 0,
                "has_documentation": self.results.get("code_quality", {}).get("documentation", 0) > 0
            }
        }
        
        # حفظ في مجلد التقارير
        reports_dir = Path("08-reports")
        reports_dir.mkdir(exist_ok=True)
        
        report_file = reports_dir / output_file
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ التقرير في: {report_file}")
        return report_file
    
    def run_full_analysis(self):
        """تشغيل التحليل الكامل"""
        print(f"🚀 بدء التحليل الشامل للمشروع: {self.project_name}")
        print("=" * 60)
        
        # تشغيل جميع التحليلات
        self.analyze_structure()
        self.analyze_dependencies()
        self.analyze_code_quality()
        self.generate_recommendations()
        
        # حفظ التقرير
        report_file = self.save_analysis_report()
        
        # عرض الملخص
        self.display_summary()
        
        return report_file
    
    def display_summary(self):
        """عرض ملخص النتائج"""
        print("\n📊 ملخص التحليل:")
        print("-" * 30)
        
        structure = self.results.get("structure", {})
        quality = self.results.get("code_quality", {})
        recommendations = self.results.get("recommendations", [])
        
        print(f"📁 اسم المشروع: {structure.get('project_name', 'غير محدد')}")
        print(f"🏷️ نوع المشروع: {', '.join(structure.get('project_types', ['غير محدد']))}")
        print(f"📄 إجمالي الملفات: {structure.get('total_files', 0)}")
        print(f"📂 إجمالي المجلدات: {structure.get('total_directories', 0)}")
        print(f"📝 ملفات التوثيق: {quality.get('documentation', 0)}")
        print(f"🧪 ملفات الاختبار: {quality.get('tests', 0)}")
        
        print(f"\n💡 التوصيات ({len(recommendations)}):")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"  {i}. {rec}")
        
        if len(recommendations) > 5:
            print(f"  ... و {len(recommendations) - 5} توصية أخرى")

def main():
    """الدالة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(description="محلل المشاريع العام")
    parser.add_argument("--project", "-p", help="مسار المشروع (افتراضي: المجلد الحالي)")
    parser.add_argument("--output", "-o", help="ملف الإخراج")
    
    args = parser.parse_args()
    
    # تحديد مسار المشروع
    project_path = args.project if args.project else "."
    
    # إنشاء المحلل
    analyzer = UniversalProjectAnalyzer(project_path)
    
    # تشغيل التحليل
    report_file = analyzer.run_full_analysis()
    
    print(f"\n🎉 تم الانتهاء من التحليل!")
    print(f"📄 التقرير محفوظ في: {report_file}")

if __name__ == "__main__":
    main()
