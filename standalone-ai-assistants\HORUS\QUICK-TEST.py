#!/usr/bin/env python3
"""
🦅 HORUS - اختبار سريع
Quick Test for HORUS System
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

def test_horus_basic():
    """اختبار أساسي لنظام HORUS"""
    print("🦅 HORUS - اختبار سريع")
    print("=" * 40)
    
    # فحص الهيكل
    print("🔍 فحص هيكل النظام...")
    
    required_dirs = [
        "MEMORY", "ANALYZERS", "REPORTS", "INTERFACES", 
        "TOOLS", "DOCS", "CONFIG"
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
        else:
            print(f"  ✅ {dir_name}/")
    
    if missing_dirs:
        print(f"  ❌ مجلدات مفقودة: {', '.join(missing_dirs)}")
    else:
        print("  ✅ جميع المجلدات موجودة")
    
    # فحص الملفات الأساسية
    print("\n📄 فحص الملفات الأساسية...")
    
    required_files = [
        "README.md",
        "START-HORUS.bat",
        "CONFIG/horus_config.json",
        "MEMORY/projects_database.json",
        "ANALYZERS/project_analyzer.py",
        "INTERFACES/main_interface.py"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
        else:
            print(f"  ✅ {file_name}")
    
    if missing_files:
        print(f"  ❌ ملفات مفقودة: {', '.join(missing_files)}")
    else:
        print("  ✅ جميع الملفات الأساسية موجودة")
    
    # اختبار الذاكرة
    print("\n🧠 اختبار الذاكرة...")
    
    try:
        memory_file = Path("MEMORY/projects_database.json")
        if memory_file.exists():
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            print(f"  ✅ الذاكرة تعمل")
            print(f"  📊 المشاريع المحللة: {memory_data.get('horus_memory', {}).get('total_projects_analyzed', 0)}")
        else:
            print("  ❌ ملف الذاكرة غير موجود")
    
    except Exception as e:
        print(f"  ❌ خطأ في الذاكرة: {e}")
    
    # اختبار التكوين
    print("\n⚙️ اختبار التكوين...")
    
    try:
        config_file = Path("CONFIG/horus_config.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            print(f"  ✅ التكوين يعمل")
            print(f"  📝 الإصدار: {config_data.get('horus', {}).get('version', 'غير محدد')}")
        else:
            print("  ❌ ملف التكوين غير موجود")
    
    except Exception as e:
        print(f"  ❌ خطأ في التكوين: {e}")
    
    # إنشاء تقرير الاختبار
    print("\n📄 إنشاء تقرير الاختبار...")
    
    test_report = {
        "test_timestamp": datetime.now().isoformat(),
        "horus_version": "1.0.0",
        "test_results": {
            "directories_check": len(missing_dirs) == 0,
            "files_check": len(missing_files) == 0,
            "memory_check": memory_file.exists() if 'memory_file' in locals() else False,
            "config_check": config_file.exists() if 'config_file' in locals() else False
        },
        "missing_components": {
            "directories": missing_dirs,
            "files": missing_files
        },
        "status": "PASS" if (len(missing_dirs) == 0 and len(missing_files) == 0) else "FAIL"
    }
    
    # حفظ تقرير الاختبار
    reports_dir = Path("REPORTS")
    reports_dir.mkdir(exist_ok=True)
    
    test_report_file = reports_dir / f"horus_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(test_report_file, 'w', encoding='utf-8') as f:
        json.dump(test_report, f, ensure_ascii=False, indent=2)
    
    print(f"  ✅ تقرير الاختبار محفوظ: {test_report_file}")
    
    # النتيجة النهائية
    print(f"\n🦅 نتيجة الاختبار: {test_report['status']}")
    
    if test_report['status'] == "PASS":
        print("🎉 نظام HORUS جاهز للاستخدام!")
        print("🚀 يمكنك تشغيله بالأمر: START-HORUS.bat")
    else:
        print("⚠️ نظام HORUS يحتاج إصلاحات")
        print("🔧 راجع المكونات المفقودة أعلاه")
    
    return test_report

def create_simple_analysis():
    """إنشاء تحليل مبسط للاختبار"""
    print("\n🔍 إنشاء تحليل مبسط...")
    
    current_dir = Path(".").resolve()
    
    # إحصائيات بسيطة
    total_files = sum(1 for f in current_dir.rglob("*") if f.is_file())
    total_dirs = sum(1 for d in current_dir.rglob("*") if d.is_dir())
    
    # أنواع الملفات
    file_types = {}
    for file_path in current_dir.rglob("*"):
        if file_path.is_file():
            ext = file_path.suffix.lower() or "no_extension"
            file_types[ext] = file_types.get(ext, 0) + 1
    
    # تحليل مبسط
    simple_analysis = {
        "analysis_id": f"simple_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "timestamp": datetime.now().isoformat(),
        "project_name": current_dir.name,
        "project_path": str(current_dir),
        "statistics": {
            "total_files": total_files,
            "total_directories": total_dirs,
            "file_types": dict(sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10])
        },
        "detected_types": [],
        "quality_estimate": "غير محدد",
        "recommendations": [
            "تشغيل التحليل الكامل للحصول على تقييم دقيق",
            "مراجعة هيكل المشروع",
            "إضافة المزيد من التوثيق"
        ]
    }
    
    # اكتشاف نوع المشروع
    if any(f.name == "package.json" for f in current_dir.rglob("package.json")):
        simple_analysis["detected_types"].append("Node.js")
    
    if any(f.name == "requirements.txt" for f in current_dir.rglob("requirements.txt")):
        simple_analysis["detected_types"].append("Python")
    
    if any(f.name.endswith(".py") for f in current_dir.rglob("*.py")):
        simple_analysis["detected_types"].append("Python")
    
    if any(f.name == "Dockerfile" for f in current_dir.rglob("Dockerfile")):
        simple_analysis["detected_types"].append("Docker")
    
    if not simple_analysis["detected_types"]:
        simple_analysis["detected_types"] = ["Unknown"]
    
    # حفظ التحليل
    reports_dir = Path("REPORTS/json")
    reports_dir.mkdir(parents=True, exist_ok=True)
    
    analysis_file = reports_dir / f"simple_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(analysis_file, 'w', encoding='utf-8') as f:
        json.dump(simple_analysis, f, ensure_ascii=False, indent=2)
    
    print(f"✅ التحليل المبسط محفوظ: {analysis_file}")
    
    # عرض النتائج
    print(f"\n📊 نتائج التحليل المبسط:")
    print(f"  📁 المشروع: {simple_analysis['project_name']}")
    print(f"  🏷️ النوع: {', '.join(simple_analysis['detected_types'])}")
    print(f"  📄 الملفات: {simple_analysis['statistics']['total_files']:,}")
    print(f"  📂 المجلدات: {simple_analysis['statistics']['total_directories']:,}")
    
    return simple_analysis

def main():
    """الدالة الرئيسية"""
    print("🦅" * 20)
    print("🦅 HORUS - اختبار النظام 🦅")
    print("🦅" * 20)
    
    # اختبار النظام
    test_result = test_horus_basic()
    
    # تحليل مبسط
    if test_result['status'] == "PASS":
        analysis_result = create_simple_analysis()
    
    print("\n🦅" * 20)
    print("🦅 انتهى الاختبار 🦅")
    print("🦅" * 20)

if __name__ == "__main__":
    main()
