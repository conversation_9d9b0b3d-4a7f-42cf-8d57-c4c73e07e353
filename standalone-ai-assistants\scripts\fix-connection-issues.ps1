# Fix Connection and Response Issues Script
# ========================================

param(
    [switch]$Force,
    [switch]$DryRun,
    [switch]$RestartServices
)

# Set UTF-8 encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🔧 Connection Issues Diagnostic & Fix" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

$issues = @{
    NetworkConnections = @()
    HangingProcesses = @()
    ServiceIssues = @()
    MemoryIssues = @()
    Recommendations = @()
}

# 1. Check network connections
Write-Host "`n🌐 Checking network connections..." -ForegroundColor Yellow

try {
    # Check for hanging network connections
    $netstat = netstat -an | Select-String "ESTABLISHED|TIME_WAIT|CLOSE_WAIT"
    $connectionCount = ($netstat | Measure-Object).Count
    
    Write-Host "   Active connections: $connectionCount" -ForegroundColor White
    
    if ($connectionCount -gt 100) {
        $issues.NetworkConnections += "Too many active connections ($connectionCount)"
        Write-Host "   ⚠️  High number of connections detected" -ForegroundColor Yellow
    }
    
    # Check for specific problematic connections
    $timeWaitConnections = $netstat | Select-String "TIME_WAIT" | Measure-Object
    if ($timeWaitConnections.Count -gt 50) {
        $issues.NetworkConnections += "Too many TIME_WAIT connections ($($timeWaitConnections.Count))"
        Write-Host "   ⚠️  Many TIME_WAIT connections found" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "   ❌ Could not check network connections: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Check for hanging processes
Write-Host "`n🔄 Checking for hanging processes..." -ForegroundColor Yellow

# Check Node.js processes with high CPU or memory
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
foreach ($proc in $nodeProcesses) {
    try {
        $cpuPercent = [math]::Round(($proc.CPU / (Get-Date).Subtract($proc.StartTime).TotalSeconds) * 100, 2)
        $memoryMB = [math]::Round($proc.WorkingSet / 1MB, 1)
        
        if ($memoryMB -gt 100 -or $cpuPercent -gt 50) {
            $issues.HangingProcesses += "Node.js PID $($proc.Id): $memoryMB MB, $cpuPercent% CPU"
            Write-Host "   ⚠️  High resource Node.js: PID $($proc.Id) - $memoryMB MB" -ForegroundColor Yellow
        }
    }
    catch {
        # Process might have ended
    }
}

# Check VS Code processes
$codeProcesses = Get-Process -Name "Code" -ErrorAction SilentlyContinue
$totalVSCodeMemory = 0
foreach ($proc in $codeProcesses) {
    $memoryMB = [math]::Round($proc.WorkingSet / 1MB, 1)
    $totalVSCodeMemory += $memoryMB
    
    if ($memoryMB -gt 500) {
        $issues.HangingProcesses += "VS Code PID $($proc.Id): $memoryMB MB (very large)"
        Write-Host "   ⚠️  Large VS Code process: PID $($proc.Id) - $memoryMB MB" -ForegroundColor Yellow
    }
}

Write-Host "   Total VS Code memory: $([math]::Round($totalVSCodeMemory, 1)) MB" -ForegroundColor White

# 3. Check system resources
Write-Host "`n💾 Checking system resources..." -ForegroundColor Yellow

try {
    $memoryInfo = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemory = [math]::Round($memoryInfo.TotalVisibleMemorySize / 1MB, 2)
    $freeMemory = [math]::Round($memoryInfo.FreePhysicalMemory / 1MB, 2)
    $usedMemory = $totalMemory - $freeMemory
    $memoryUsagePercent = [math]::Round(($usedMemory / $totalMemory) * 100, 1)
    
    Write-Host "   Memory usage: $usedMemory GB / $totalMemory GB ($memoryUsagePercent%)" -ForegroundColor White
    
    if ($memoryUsagePercent -gt 85) {
        $issues.MemoryIssues += "Critical memory usage: $memoryUsagePercent%"
        Write-Host "   🔴 CRITICAL: Very high memory usage!" -ForegroundColor Red
    }
    elseif ($memoryUsagePercent -gt 75) {
        $issues.MemoryIssues += "High memory usage: $memoryUsagePercent%"
        Write-Host "   ⚠️  HIGH: Memory usage is high" -ForegroundColor Yellow
    }
    
    # Check available handles
    $processCount = (Get-Process).Count
    Write-Host "   Running processes: $processCount" -ForegroundColor White
    
    if ($processCount -gt 200) {
        $issues.HangingProcesses += "Too many processes running: $processCount"
        Write-Host "   ⚠️  Many processes running" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "   ❌ Could not check system resources" -ForegroundColor Red
}

# 4. Check Docker services (if applicable)
Write-Host "`n🐳 Checking Docker services..." -ForegroundColor Yellow

try {
    $dockerRunning = Get-Process -Name "Docker Desktop" -ErrorAction SilentlyContinue
    if ($dockerRunning) {
        Write-Host "   ✅ Docker Desktop is running" -ForegroundColor Green
        
        # Check Docker containers
        $dockerContainers = docker ps --format "table {{.Names}}\t{{.Status}}" 2>$null
        if ($dockerContainers) {
            $containerCount = ($dockerContainers | Measure-Object).Count - 1 # Subtract header
            Write-Host "   Running containers: $containerCount" -ForegroundColor White
            
            if ($containerCount -gt 10) {
                $issues.ServiceIssues += "Many Docker containers running: $containerCount"
                Write-Host "   ⚠️  Many containers running" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "   ⚠️  Docker Desktop not running" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "   ⚠️  Could not check Docker status" -ForegroundColor Yellow
}

# 5. Generate recommendations
Write-Host "`n💡 RECOMMENDATIONS:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

if ($issues.NetworkConnections.Count -gt 0) {
    Write-Host "🌐 Network Issues:" -ForegroundColor Yellow
    foreach ($issue in $issues.NetworkConnections) {
        Write-Host "   • $issue" -ForegroundColor White
        $issues.Recommendations += "Reset network connections"
    }
}

if ($issues.HangingProcesses.Count -gt 0) {
    Write-Host "🔄 Process Issues:" -ForegroundColor Yellow
    foreach ($issue in $issues.HangingProcesses) {
        Write-Host "   • $issue" -ForegroundColor White
        $issues.Recommendations += "Kill hanging processes"
    }
}

if ($issues.MemoryIssues.Count -gt 0) {
    Write-Host "💾 Memory Issues:" -ForegroundColor Yellow
    foreach ($issue in $issues.MemoryIssues) {
        Write-Host "   • $issue" -ForegroundColor White
        $issues.Recommendations += "Free up memory"
    }
}

if ($issues.ServiceIssues.Count -gt 0) {
    Write-Host "🔧 Service Issues:" -ForegroundColor Yellow
    foreach ($issue in $issues.ServiceIssues) {
        Write-Host "   • $issue" -ForegroundColor White
        $issues.Recommendations += "Restart services"
    }
}

# 6. Apply fixes
$totalIssues = $issues.NetworkConnections.Count + $issues.HangingProcesses.Count + $issues.MemoryIssues.Count + $issues.ServiceIssues.Count

if ($totalIssues -eq 0) {
    Write-Host "`n✅ No major issues detected!" -ForegroundColor Green
    Write-Host "The 'connection disposed' error might be temporary." -ForegroundColor White
} else {
    Write-Host "`n🚀 APPLYING FIXES:" -ForegroundColor Green
    Write-Host "=================" -ForegroundColor Green
    
    if (-not $DryRun) {
        # Fix 1: Clear DNS cache
        Write-Host "1. Clearing DNS cache..." -ForegroundColor White
        try {
            ipconfig /flushdns | Out-Null
            Write-Host "   ✅ DNS cache cleared" -ForegroundColor Green
        }
        catch {
            Write-Host "   ❌ Failed to clear DNS cache" -ForegroundColor Red
        }
        
        # Fix 2: Reset network stack
        Write-Host "2. Resetting network stack..." -ForegroundColor White
        try {
            netsh winsock reset | Out-Null
            Write-Host "   ✅ Winsock reset (restart required)" -ForegroundColor Green
        }
        catch {
            Write-Host "   ❌ Failed to reset Winsock" -ForegroundColor Red
        }
        
        # Fix 3: Kill hanging Node.js processes
        if ($issues.HangingProcesses.Count -gt 0) {
            Write-Host "3. Killing hanging processes..." -ForegroundColor White
            $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
            $killed = 0
            
            foreach ($proc in $nodeProcesses) {
                try {
                    $memoryMB = [math]::Round($proc.WorkingSet / 1MB, 1)
                    if ($memoryMB -gt 50) {
                        $wmi = Get-WmiObject Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue
                        $cmdLine = if ($wmi) { $wmi.CommandLine } else { "" }
                        
                        # Don't kill critical services
                        if ($cmdLine -notmatch "anythingllm|ollama|n8n|ai-coordinator") {
                            Stop-Process -Id $proc.Id -Force
                            Write-Host "   ✅ Killed Node.js PID $($proc.Id) ($memoryMB MB)" -ForegroundColor Green
                            $killed++
                        }
                    }
                }
                catch {
                    # Process might have ended
                }
            }
            Write-Host "   📊 Killed $killed hanging processes" -ForegroundColor White
        }
        
        # Fix 4: Restart services if requested
        if ($RestartServices) {
            Write-Host "4. Restarting services..." -ForegroundColor White
            try {
                # Restart Docker if running
                $dockerRunning = Get-Process -Name "Docker Desktop" -ErrorAction SilentlyContinue
                if ($dockerRunning) {
                    Write-Host "   🔄 Restarting Docker..." -ForegroundColor Yellow
                    Stop-Process -Name "Docker Desktop" -Force -ErrorAction SilentlyContinue
                    Start-Sleep -Seconds 5
                    Start-Process -FilePath "C:\Program Files\Docker\Docker\Docker Desktop.exe" -WindowStyle Hidden -ErrorAction SilentlyContinue
                    Write-Host "   ✅ Docker restart initiated" -ForegroundColor Green
                }
            }
            catch {
                Write-Host "   ❌ Failed to restart Docker" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "🔍 DRY RUN MODE - Would apply these fixes:" -ForegroundColor Yellow
        Write-Host "   • Clear DNS cache" -ForegroundColor White
        Write-Host "   • Reset network stack" -ForegroundColor White
        Write-Host "   • Kill hanging processes" -ForegroundColor White
        if ($RestartServices) {
            Write-Host "   • Restart services" -ForegroundColor White
        }
    }
}

# 7. Immediate actions for connection issues
Write-Host "`n🔧 IMMEDIATE ACTIONS FOR CONNECTION ISSUES:" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan

Write-Host "1. Close unnecessary browser tabs and applications" -ForegroundColor White
Write-Host "2. Restart VS Code if it's consuming too much memory" -ForegroundColor White
Write-Host "3. Wait 30 seconds before retrying the operation" -ForegroundColor White
Write-Host "4. If using Docker, restart Docker Desktop" -ForegroundColor White
Write-Host "5. Check internet connection stability" -ForegroundColor White

# 8. Prevention tips
Write-Host "`n🛡️  PREVENTION TIPS:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

Write-Host "• Run cleanup scripts regularly" -ForegroundColor White
Write-Host "• Monitor memory usage" -ForegroundColor White
Write-Host "• Close unused VS Code windows" -ForegroundColor White
Write-Host "• Clear npm cache weekly: npm cache clean --force" -ForegroundColor White
Write-Host "• Restart development tools daily" -ForegroundColor White

if ($DryRun) {
    Write-Host "`n🔍 DRY RUN COMPLETED" -ForegroundColor Yellow
    Write-Host "Run without -DryRun to apply fixes" -ForegroundColor Yellow
} else {
    Write-Host "`n✅ CONNECTION ISSUE FIXES APPLIED!" -ForegroundColor Green
    Write-Host "Wait 30 seconds before retrying your operation" -ForegroundColor Yellow
}
