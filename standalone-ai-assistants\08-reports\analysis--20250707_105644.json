{"analysis_timestamp": "2025-07-07T10:56:44.633144", "analyzer": "Universal Project Analyzer", "project": "", "project_path": ".", "analysis_results": {"structure": {"project_name": "", "project_path": ".", "project_types": ["Python", "Web"], "total_files": 29, "total_directories": 4, "file_types": {".py": 11, ".md": 5, ".ps1": 5, ".json": 4, ".bat": 3, ".html": 1}, "large_files": [], "main_directories": ["gemini-interface", "logs", "scripts", "shared-memory"]}, "dependencies": {}, "code_quality": {"documentation": 5, "tests": 3, "config_files": 4, "code_files": 11, "doc_ratio": "21.7%", "test_ratio": "13.0%"}, "recommendations": ["إضافة virtual environment للمشروع", "استخدام black لتنسيق الكود", "إضافة pytest للاختبارات", "استخدام pre-commit hooks", "إضافة المزيد من الاختبارات"]}, "summary": {"project_types": ["Python", "Web"], "total_files": 29, "main_languages": [".py", ".md", ".ps1", ".json", ".bat"], "has_tests": true, "has_documentation": true}}