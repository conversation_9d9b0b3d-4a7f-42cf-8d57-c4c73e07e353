#!/usr/bin/env python3
"""
🧠 HORUS - مدير الذاكرة المتقدم
Advanced Memory Manager for HORUS System
"""

import json
import uuid
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict

class MemoryManager:
    """مدير الذاكرة المتقدم لنظام HORUS"""
    
    def __init__(self):
        self.memory_dir = Path(__file__).parent
        self.projects_db_file = self.memory_dir / "projects_database.json"
        self.history_file = self.memory_dir / "analysis_history.json"
        self.learning_file = self.memory_dir / "learning_data.json"
        
        # تهيئة ملفات الذاكرة
        self.initialize_memory_files()
    
    def initialize_memory_files(self):
        """تهيئة ملفات الذاكرة"""
        # إنشاء المجلد إذا لم يكن موجوداً
        self.memory_dir.mkdir(exist_ok=True)
        
        # تهيئة قاعدة بيانات المشاريع
        if not self.projects_db_file.exists():
            self.create_empty_projects_db()
        
        # تهيئة تاريخ التحليلات
        if not self.history_file.exists():
            self.create_empty_history()
        
        # تهيئة بيانات التعلم
        if not self.learning_file.exists():
            self.create_empty_learning_data()
    
    def create_empty_projects_db(self):
        """إنشاء قاعدة بيانات فارغة للمشاريع"""
        empty_db = {
            "horus_memory": {
                "version": "1.0.0",
                "created": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_projects_analyzed": 0,
                "memory_size_mb": 0.1
            },
            "projects": {},
            "analysis_history": [],
            "learning_patterns": {
                "common_project_types": {},
                "frequent_issues": {},
                "best_practices": {},
                "optimization_suggestions": {}
            },
            "user_preferences": {
                "default_analysis_depth": "deep",
                "preferred_report_format": "json",
                "auto_save_reports": True,
                "enable_learning": True
            },
            "statistics": {
                "most_analyzed_language": None,
                "average_project_size": 0,
                "common_frameworks": {},
                "analysis_success_rate": 100.0
            }
        }
        
        with open(self.projects_db_file, 'w', encoding='utf-8') as f:
            json.dump(empty_db, f, ensure_ascii=False, indent=2)
    
    def create_empty_history(self):
        """إنشاء تاريخ فارغ للتحليلات"""
        empty_history = {
            "history_metadata": {
                "version": "1.0.0",
                "created": datetime.now().isoformat(),
                "total_analyses": 0,
                "retention_days": 365
            },
            "analyses": [],
            "trends": {
                "monthly_analysis_count": {},
                "popular_project_types": {},
                "improvement_trends": {},
                "common_recommendations": {}
            },
            "comparisons": {
                "before_after": [],
                "project_similarities": [],
                "performance_improvements": []
            }
        }
        
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(empty_history, f, ensure_ascii=False, indent=2)
    
    def create_empty_learning_data(self):
        """إنشاء بيانات تعلم فارغة"""
        empty_learning = {
            "learning_metadata": {
                "version": "1.0.0",
                "created": datetime.now().isoformat(),
                "learning_enabled": True,
                "total_patterns": 0
            },
            "patterns": {
                "project_type_patterns": {},
                "quality_patterns": {},
                "recommendation_patterns": {},
                "success_patterns": {}
            },
            "insights": {
                "best_practices": [],
                "common_mistakes": [],
                "optimization_tips": [],
                "framework_recommendations": {}
            }
        }
        
        with open(self.learning_file, 'w', encoding='utf-8') as f:
            json.dump(empty_learning, f, ensure_ascii=False, indent=2)
    
    def save_analysis(self, analysis_results):
        """حفظ نتائج التحليل في الذاكرة"""
        try:
            # تحديث قاعدة بيانات المشاريع
            self.update_projects_database(analysis_results)
            
            # إضافة إلى تاريخ التحليلات
            self.add_to_history(analysis_results)
            
            # تحديث بيانات التعلم
            self.update_learning_data(analysis_results)
            
            print("✅ تم حفظ التحليل في الذاكرة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ التحليل: {e}")
    
    def update_projects_database(self, analysis_results):
        """تحديث قاعدة بيانات المشاريع"""
        # قراءة قاعدة البيانات الحالية
        with open(self.projects_db_file, 'r', encoding='utf-8') as f:
            db = json.load(f)
        
        # استخراج معلومات المشروع
        metadata = analysis_results.get("metadata", {})
        project_name = metadata.get("project_name", "unknown")
        project_path = metadata.get("project_path", "")
        analysis_id = metadata.get("analysis_id", str(uuid.uuid4()))
        
        # إنشاء مفتاح فريد للمشروع
        project_key = f"{project_name}_{hash(project_path) % 10000}"
        
        # تحديث معلومات المشروع
        if project_key not in db["projects"]:
            db["projects"][project_key] = {
                "name": project_name,
                "path": project_path,
                "first_analyzed": datetime.now().isoformat(),
                "analysis_count": 0,
                "analyses": []
            }
        
        # إضافة التحليل الجديد
        db["projects"][project_key]["last_analyzed"] = datetime.now().isoformat()
        db["projects"][project_key]["analysis_count"] += 1
        db["projects"][project_key]["analyses"].append({
            "analysis_id": analysis_id,
            "timestamp": metadata.get("timestamp"),
            "summary": self.create_analysis_summary(analysis_results)
        })
        
        # تحديث الإحصائيات العامة
        db["horus_memory"]["total_projects_analyzed"] = len(db["projects"])
        db["horus_memory"]["last_updated"] = datetime.now().isoformat()
        
        # حفظ قاعدة البيانات المحدثة
        with open(self.projects_db_file, 'w', encoding='utf-8') as f:
            json.dump(db, f, ensure_ascii=False, indent=2)
    
    def add_to_history(self, analysis_results):
        """إضافة التحليل إلى التاريخ"""
        # قراءة التاريخ الحالي
        with open(self.history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)
        
        # إنشاء سجل التحليل
        analysis_record = {
            "analysis_id": analysis_results.get("metadata", {}).get("analysis_id"),
            "timestamp": analysis_results.get("metadata", {}).get("timestamp"),
            "project_name": analysis_results.get("metadata", {}).get("project_name"),
            "project_types": analysis_results.get("structure", {}).get("project_types", []),
            "quality_score": analysis_results.get("quality", {}).get("quality_metrics", {}).get("overall_quality_score", 0),
            "recommendations_count": len(analysis_results.get("recommendations", [])),
            "file_count": analysis_results.get("structure", {}).get("file_statistics", {}).get("total_files", 0)
        }
        
        # إضافة إلى التاريخ
        history["analyses"].append(analysis_record)
        history["history_metadata"]["total_analyses"] += 1
        
        # تنظيف التاريخ القديم (الاحتفاظ بآخر 365 يوم)
        self.cleanup_old_history(history)
        
        # حفظ التاريخ المحدث
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    
    def update_learning_data(self, analysis_results):
        """تحديث بيانات التعلم"""
        # قراءة بيانات التعلم الحالية
        with open(self.learning_file, 'r', encoding='utf-8') as f:
            learning = json.load(f)
        
        # استخراج الأنماط من التحليل
        project_types = analysis_results.get("structure", {}).get("project_types", [])
        quality_score = analysis_results.get("quality", {}).get("quality_metrics", {}).get("overall_quality_score", 0)
        recommendations = analysis_results.get("recommendations", [])
        
        # تحديث أنماط أنواع المشاريع
        for project_type in project_types:
            if isinstance(project_type, dict):
                type_name = project_type.get("type", "unknown")
            else:
                type_name = project_type
            
            if type_name not in learning["patterns"]["project_type_patterns"]:
                learning["patterns"]["project_type_patterns"][type_name] = {
                    "count": 0,
                    "average_quality": 0,
                    "common_issues": [],
                    "best_practices": []
                }
            
            pattern = learning["patterns"]["project_type_patterns"][type_name]
            pattern["count"] += 1
            
            # تحديث متوسط الجودة
            current_avg = pattern["average_quality"]
            new_avg = (current_avg * (pattern["count"] - 1) + quality_score) / pattern["count"]
            pattern["average_quality"] = round(new_avg, 2)
        
        # تحديث أنماط التوصيات
        for recommendation in recommendations:
            rec_type = recommendation.get("type", "general")
            if rec_type not in learning["patterns"]["recommendation_patterns"]:
                learning["patterns"]["recommendation_patterns"][rec_type] = {
                    "frequency": 0,
                    "common_titles": [],
                    "effectiveness": 0
                }
            
            learning["patterns"]["recommendation_patterns"][rec_type]["frequency"] += 1
        
        # تحديث الإحصائيات
        learning["learning_metadata"]["total_patterns"] = len(learning["patterns"]["project_type_patterns"])
        
        # حفظ بيانات التعلم المحدثة
        with open(self.learning_file, 'w', encoding='utf-8') as f:
            json.dump(learning, f, ensure_ascii=False, indent=2)
    
    def get_smart_recommendations(self, project_types, total_files, test_ratio, doc_ratio):
        """الحصول على توصيات ذكية بناءً على التعلم"""
        recommendations = []
        
        try:
            # قراءة بيانات التعلم
            with open(self.learning_file, 'r', encoding='utf-8') as f:
                learning = json.load(f)
            
            # توصيات بناءً على الأنماط المتعلمة
            for project_type in project_types:
                if isinstance(project_type, dict):
                    type_name = project_type.get("type", "unknown")
                else:
                    type_name = project_type
                
                if type_name in learning["patterns"]["project_type_patterns"]:
                    pattern = learning["patterns"]["project_type_patterns"][type_name]
                    avg_quality = pattern["average_quality"]
                    
                    # توصيات بناءً على متوسط الجودة للنوع
                    if avg_quality > 80:
                        recommendations.append({
                            "type": "learning",
                            "priority": "low",
                            "title": f"مشروع {type_name} ممتاز",
                            "description": f"هذا النوع من المشاريع يحقق عادة جودة عالية ({avg_quality:.1f}%)"
                        })
                    elif avg_quality < 60:
                        recommendations.append({
                            "type": "learning",
                            "priority": "medium",
                            "title": f"تحسين مشروع {type_name}",
                            "description": f"مشاريع {type_name} تحتاج عادة تحسين (متوسط الجودة: {avg_quality:.1f}%)"
                        })
            
            # توصيات بناءً على المقارنة مع المشاريع المشابهة
            if test_ratio < 15:
                recommendations.append({
                    "type": "learning",
                    "priority": "high",
                    "title": "زيادة الاختبارات بناءً على التجربة",
                    "description": "المشاريع المشابهة التي تحتوي على اختبارات أكثر تحقق جودة أفضل"
                })
            
            if doc_ratio < 10:
                recommendations.append({
                    "type": "learning",
                    "priority": "medium",
                    "title": "تحسين التوثيق بناءً على أفضل الممارسات",
                    "description": "التوثيق الجيد يحسن من قابلية الصيانة والتطوير"
                })
        
        except Exception as e:
            # في حالة الخطأ، إرجاع توصيات افتراضية
            pass
        
        return recommendations
    
    def create_analysis_summary(self, analysis_results):
        """إنشاء ملخص للتحليل"""
        structure = analysis_results.get("structure", {})
        quality = analysis_results.get("quality", {})
        
        return {
            "project_types": structure.get("project_types", []),
            "total_files": structure.get("file_statistics", {}).get("total_files", 0),
            "quality_score": quality.get("quality_metrics", {}).get("overall_quality_score", 0),
            "quality_grade": quality.get("quality_metrics", {}).get("quality_grade", "غير محدد"),
            "recommendations_count": len(analysis_results.get("recommendations", []))
        }
    
    def cleanup_old_history(self, history):
        """تنظيف التاريخ القديم"""
        retention_days = history["history_metadata"]["retention_days"]
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        # تصفية التحليلات القديمة
        filtered_analyses = []
        for analysis in history["analyses"]:
            try:
                analysis_date = datetime.fromisoformat(analysis["timestamp"])
                if analysis_date > cutoff_date:
                    filtered_analyses.append(analysis)
            except:
                # الاحتفاظ بالتحليلات التي لا يمكن تحليل تاريخها
                filtered_analyses.append(analysis)
        
        history["analyses"] = filtered_analyses
    
    def get_project_history(self, project_name):
        """الحصول على تاريخ مشروع معين"""
        try:
            with open(self.projects_db_file, 'r', encoding='utf-8') as f:
                db = json.load(f)
            
            # البحث عن المشروع
            for project_key, project_data in db["projects"].items():
                if project_data["name"] == project_name:
                    return project_data
            
            return None
        
        except Exception as e:
            return None
    
    def get_memory_statistics(self):
        """الحصول على إحصائيات الذاكرة"""
        try:
            with open(self.projects_db_file, 'r', encoding='utf-8') as f:
                db = json.load(f)
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            return {
                "total_projects": len(db["projects"]),
                "total_analyses": history["history_metadata"]["total_analyses"],
                "memory_created": db["horus_memory"]["created"],
                "last_updated": db["horus_memory"]["last_updated"],
                "most_analyzed_projects": self.get_most_analyzed_projects(db),
                "recent_analyses": history["analyses"][-5:] if history["analyses"] else []
            }
        
        except Exception as e:
            return {"error": str(e)}
    
    def get_most_analyzed_projects(self, db):
        """الحصول على أكثر المشاريع تحليلاً"""
        projects = []
        for project_key, project_data in db["projects"].items():
            projects.append({
                "name": project_data["name"],
                "analysis_count": project_data["analysis_count"],
                "last_analyzed": project_data.get("last_analyzed", "غير محدد")
            })
        
        # ترتيب حسب عدد التحليلات
        projects.sort(key=lambda x: x["analysis_count"], reverse=True)
        
        return projects[:5]  # أكثر 5 مشاريع
