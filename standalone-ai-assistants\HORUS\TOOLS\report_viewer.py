#!/usr/bin/env python3
"""
📄 HORUS - عارض التقارير
Report Viewer for HORUS System
"""

import json
from pathlib import Path
from datetime import datetime

class ReportViewer:
    """عارض التقارير لنظام HORUS"""
    
    def __init__(self):
        self.reports_dir = Path(__file__).parent.parent / "REPORTS" / "json"
    
    def list_reports(self):
        """قائمة التقارير المتاحة"""
        if not self.reports_dir.exists():
            return []
        
        report_files = list(self.reports_dir.glob("horus_analysis_*.json"))
        report_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        return report_files
    
    def view_report(self, report_file):
        """عرض تقرير محدد"""
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            self.display_report_summary(report)
            return report
        
        except Exception as e:
            print(f"❌ خطأ في قراءة التقرير: {e}")
            return None
    
    def display_report_summary(self, report):
        """عرض ملخص التقرير"""
        print("📄 ملخص التقرير")
        print("=" * 30)
        
        metadata = report.get("metadata", {})
        structure = report.get("structure", {})
        quality = report.get("quality", {})
        
        print(f"📁 المشروع: {metadata.get('project_name', 'غير محدد')}")
        print(f"📅 التاريخ: {metadata.get('timestamp', 'غير محدد')[:16]}")
        print(f"🏷️ النوع: {self.format_project_types(structure.get('project_types', []))}")
        print(f"📊 الجودة: {quality.get('quality_metrics', {}).get('overall_quality_score', 0):.1f}/100")
    
    def format_project_types(self, project_types):
        """تنسيق أنواع المشروع"""
        if not project_types:
            return "غير محدد"
        
        types = []
        for ptype in project_types:
            if isinstance(ptype, dict):
                types.append(ptype.get("type", "غير محدد"))
            else:
                types.append(str(ptype))
        
        return ", ".join(types[:3])
