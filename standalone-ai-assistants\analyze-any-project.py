#!/usr/bin/env python3
"""
واجهة تحليل أي مشروع - Analyze Any Project Interface
"""

import os
import sys
from pathlib import Path
from universal_project_analyzer import UniversalProjectAnalyzer

class ProjectAnalysisInterface:
    """واجهة تحليل المشاريع"""
    
    def __init__(self):
        self.current_project = None
        self.analyzer = None
    
    def show_welcome(self):
        """عرض الترحيب"""
        print("🔍 محلل المشاريع العام - Universal Project Analyzer")
        print("=" * 60)
        print("يمكن تحليل أي نوع من المشاريع البرمجية!")
        print()
        print("🎯 الخيارات المتاحة:")
        print("1. 📁 تحليل المشروع الحالي")
        print("2. 📂 تحليل مشروع آخر")
        print("3. 📊 عرض آخر تحليل")
        print("4. 💡 عرض التوصيات")
        print("5. 📋 قائمة المشاريع المحللة")
        print("0. 🚪 خروج")
        print("=" * 60)
    
    def analyze_current_project(self):
        """تحليل المشروع الحالي"""
        current_dir = Path(".").resolve()
        print(f"\n🔍 تحليل المشروع الحالي: {current_dir.name}")
        print(f"📁 المسار: {current_dir}")
        
        confirm = input("\n❓ هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ تم إلغاء التحليل")
            return
        
        self.analyzer = UniversalProjectAnalyzer(current_dir)
        self.current_project = current_dir
        
        try:
            report_file = self.analyzer.run_full_analysis()
            print(f"\n✅ تم التحليل بنجاح!")
            print(f"📄 التقرير: {report_file}")
        except Exception as e:
            print(f"❌ خطأ في التحليل: {e}")
    
    def analyze_other_project(self):
        """تحليل مشروع آخر"""
        print("\n📂 تحليل مشروع آخر")
        print("-" * 30)
        
        project_path = input("📁 أدخل مسار المشروع: ").strip()
        if not project_path:
            print("❌ لم تدخل مسار")
            return
        
        project_path = Path(project_path)
        if not project_path.exists():
            print(f"❌ المسار غير موجود: {project_path}")
            return
        
        if not project_path.is_dir():
            print(f"❌ المسار ليس مجلد: {project_path}")
            return
        
        print(f"🔍 تحليل المشروع: {project_path.name}")
        print(f"📁 المسار: {project_path}")
        
        self.analyzer = UniversalProjectAnalyzer(project_path)
        self.current_project = project_path
        
        try:
            report_file = self.analyzer.run_full_analysis()
            print(f"\n✅ تم التحليل بنجاح!")
            print(f"📄 التقرير: {report_file}")
        except Exception as e:
            print(f"❌ خطأ في التحليل: {e}")
    
    def show_last_analysis(self):
        """عرض آخر تحليل"""
        if not self.analyzer:
            print("❌ لا يوجد تحليل سابق")
            return
        
        print("\n📊 آخر تحليل:")
        print("-" * 30)
        self.analyzer.display_summary()
    
    def show_recommendations(self):
        """عرض التوصيات"""
        if not self.analyzer:
            print("❌ لا يوجد تحليل سابق")
            return
        
        recommendations = self.analyzer.results.get("recommendations", [])
        if not recommendations:
            print("❌ لا توجد توصيات")
            return
        
        print(f"\n💡 التوصيات للمشروع: {self.current_project.name}")
        print("-" * 40)
        
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    def list_analyzed_projects(self):
        """قائمة المشاريع المحللة"""
        reports_dir = Path("08-reports")
        if not reports_dir.exists():
            print("❌ لا توجد تقارير محفوظة")
            return
        
        report_files = list(reports_dir.glob("analysis-*.json"))
        if not report_files:
            print("❌ لا توجد تقارير محفوظة")
            return
        
        print(f"\n📋 المشاريع المحللة ({len(report_files)}):")
        print("-" * 40)
        
        for i, report_file in enumerate(sorted(report_files, reverse=True)[:10], 1):
            # استخراج اسم المشروع من اسم الملف
            name_parts = report_file.stem.split('-')
            if len(name_parts) >= 2:
                project_name = name_parts[1]
                timestamp = name_parts[-1] if len(name_parts) > 2 else "غير محدد"
                print(f"  {i}. {project_name} ({timestamp})")
            else:
                print(f"  {i}. {report_file.stem}")
    
    def run(self):
        """تشغيل الواجهة"""
        while True:
            self.show_welcome()
            
            try:
                choice = input("\n🎯 اختر رقم الخيار: ").strip()
                
                if choice == "1":
                    self.analyze_current_project()
                elif choice == "2":
                    self.analyze_other_project()
                elif choice == "3":
                    self.show_last_analysis()
                elif choice == "4":
                    self.show_recommendations()
                elif choice == "5":
                    self.list_analyzed_projects()
                elif choice == "0":
                    print("\n👋 شكراً لاستخدام محلل المشاريع!")
                    break
                else:
                    print("❌ اختيار غير صحيح")
                
                input("\n⏸️ اضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
                break
            except Exception as e:
                print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    interface = ProjectAnalysisInterface()
    interface.run()

if __name__ == "__main__":
    main()
