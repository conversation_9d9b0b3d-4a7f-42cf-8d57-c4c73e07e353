{"horus": {"version": "1.0.0", "name": "HORUS Advanced Project Analyzer", "description": "نظام متقدم لتحليل المشاريع البرمجية", "created": "2025-07-07", "developer": "Augment Agent"}, "analysis": {"default_depth": "deep", "enable_learning": true, "auto_save_reports": true, "max_file_size_mb": 100, "timeout_seconds": 300, "supported_languages": ["Python", "JavaScript", "TypeScript", "Java", "C#", "PHP", "<PERSON>", "Go", "Rust", "C++", "Swift", "<PERSON><PERSON><PERSON>", "Scala"]}, "memory": {"enable_memory": true, "retention_days": 365, "auto_cleanup": true, "max_memory_size_mb": 500, "backup_frequency": "weekly"}, "reports": {"default_format": "json", "supported_formats": ["json", "html"], "include_recommendations": true, "include_detailed_analysis": true, "compress_large_reports": true}, "interface": {"default_interface": "main", "show_progress": true, "colored_output": true, "verbose_mode": false}, "security": {"scan_for_secrets": false, "check_dependencies": true, "analyze_permissions": false}, "performance": {"parallel_analysis": true, "max_threads": 4, "cache_results": true, "optimize_memory": true}, "paths": {"reports_dir": "REPORTS", "memory_dir": "MEMORY", "cache_dir": "CACHE", "logs_dir": "LOGS"}}