#!/usr/bin/env python3
"""
واجهة بسيطة لاستخدام النظام المعزول
Simple Interface for Isolated System
"""

import os
import json
from pathlib import Path
from datetime import datetime

class SimpleInterface:
    """واجهة بسيطة للنظام المعزول"""
    
    def __init__(self):
        self.memory_file = Path("shared-memory/project-knowledge.json")
        self.session_file = Path("shared-memory/quick-session.json")
        
    def show_welcome(self):
        """عرض الترحيب"""
        print("🤖 مرحباً بك في النظام المعزول!")
        print("=" * 50)
        print("🎯 الاستخدامات المتاحة:")
        print("1. 📊 تحليل المشروع الكبير")
        print("2. 🧠 عرض الذاكرة المشتركة")
        print("3. 📝 إنشاء تقرير جديد")
        print("4. 🔍 البحث في الملفات")
        print("5. 📋 عرض حالة النظام")
        print("0. 🚪 خروج")
        print("=" * 50)
        
    def analyze_big_project(self):
        """تحليل المشروع الكبير"""
        print("\n🔍 تشغيل تحليل المشروع الكبير...")
        try:
            os.system("python analyze-big-project.py")
            print("✅ تم التحليل بنجاح!")
        except Exception as e:
            print(f"❌ خطأ في التحليل: {e}")
    
    def show_memory(self):
        """عرض الذاكرة المشتركة"""
        print("\n🧠 الذاكرة المشتركة:")
        print("-" * 30)
        
        if self.memory_file.exists():
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory = json.load(f)
                print(f"📁 المشروع: {memory.get('project_name', 'غير محدد')}")
                print(f"📅 تاريخ الإنشاء: {memory.get('created_date', 'غير محدد')}")
                print(f"🛡️ حالة العزل: {memory.get('isolation_status', 'معزول')}")
                print(f"📊 عدد الجلسات: {len(memory.get('sessions', []))}")
        else:
            print("❌ لا توجد ذاكرة محفوظة")
    
    def create_report(self):
        """إنشاء تقرير جديد"""
        print("\n📝 إنشاء تقرير جديد...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "type": "manual_report",
            "status": "تم الإنشاء يدوياً",
            "content": {
                "system_status": "نشط",
                "memory_status": "متاح",
                "isolation_status": "معزول بنجاح"
            }
        }
        
        report_file = Path(f"08-reports/manual-report-{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم إنشاء التقرير: {report_file}")
    
    def search_files(self):
        """البحث في الملفات"""
        print("\n🔍 البحث في الملفات:")
        print("-" * 30)
        
        search_term = input("🔎 أدخل كلمة البحث: ").strip()
        if not search_term:
            print("❌ لم تدخل كلمة بحث")
            return
        
        found_files = []
        for root, dirs, files in os.walk("."):
            for file in files:
                if search_term.lower() in file.lower():
                    found_files.append(os.path.join(root, file))
        
        if found_files:
            print(f"✅ تم العثور على {len(found_files)} ملف:")
            for i, file in enumerate(found_files[:10], 1):
                print(f"  {i}. {file}")
            if len(found_files) > 10:
                print(f"  ... و {len(found_files) - 10} ملف آخر")
        else:
            print("❌ لم يتم العثور على ملفات")
    
    def show_system_status(self):
        """عرض حالة النظام"""
        print("\n📋 حالة النظام:")
        print("-" * 30)
        
        # فحص الملفات الأساسية
        essential_files = [
            "README.md",
            "quick-start.py", 
            "analyze-big-project.py",
            "shared-memory/project-knowledge.json"
        ]
        
        print("📁 الملفات الأساسية:")
        for file in essential_files:
            status = "✅" if Path(file).exists() else "❌"
            print(f"  {status} {file}")
        
        # فحص المجلدات
        essential_dirs = [
            "shared-memory",
            "gemini-interface", 
            "scripts",
            "logs"
        ]
        
        print("\n📂 المجلدات:")
        for dir_name in essential_dirs:
            status = "✅" if Path(dir_name).exists() else "❌"
            print(f"  {status} {dir_name}/")
        
        # إحصائيات
        total_files = sum(1 for _ in Path(".").rglob("*") if _.is_file())
        total_dirs = sum(1 for _ in Path(".").rglob("*") if _.is_dir())
        
        print(f"\n📊 الإحصائيات:")
        print(f"  📄 إجمالي الملفات: {total_files}")
        print(f"  📁 إجمالي المجلدات: {total_dirs}")
    
    def run(self):
        """تشغيل الواجهة"""
        while True:
            self.show_welcome()
            
            try:
                choice = input("\n🎯 اختر رقم الخيار: ").strip()
                
                if choice == "1":
                    self.analyze_big_project()
                elif choice == "2":
                    self.show_memory()
                elif choice == "3":
                    self.create_report()
                elif choice == "4":
                    self.search_files()
                elif choice == "5":
                    self.show_system_status()
                elif choice == "0":
                    print("\n👋 شكراً لاستخدام النظام المعزول!")
                    break
                else:
                    print("❌ اختيار غير صحيح")
                
                input("\n⏸️ اضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
                break
            except Exception as e:
                print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    interface = SimpleInterface()
    interface.run()

if __name__ == "__main__":
    main()
